import { createPortal } from 'react-dom'
import PropTypes from 'prop-types'
import { Dialog, DialogContent, DialogTitle } from '@mui/material'
import { CodeEditor } from '@/ui-component/editor/CodeEditor'

const overrideConfig = `{
    overrideConfig: {
        vars: {
            var1: 'abc'
        }
    }
}`

const HowToUseVariablesDialog = ({ show, onCancel }) => {
  const portalElement = document.getElementById('portal')

  const component = show ? (
    <Dialog
      onClose={onCancel}
      open={show}
      fullWidth
      maxWidth='sm'
      aria-labelledby='alert-dialog-title'
      aria-describedby='alert-dialog-description'
    >
      <DialogTitle sx={{ fontSize: '1rem' }} id='alert-dialog-title'>
        Cách Sử Dụng Biến
      </DialogTitle>
      <DialogContent>
        <p style={{ marginBottom: '10px' }}>
          Biến có thể được sử dụng trong Công Cụ Tùy Chỉnh, <PERSON><PERSON><PERSON>ù<PERSON> Chỉnh, <PERSON><PERSON><PERSON><PERSON>ả<PERSON> Tù<PERSON> Chỉnh, <PERSON>àm If Else với tiền tố $.
        </p>
        <CodeEditor
          disabled={true}
          value={`$vars.<variable-name>`}
          height={'50px'}
          theme={'dark'}
          lang={'js'}
          basicSetup={{ highlightActiveLine: false, highlightActiveLineGutter: false }}
        />
        <p style={{ marginBottom: '10px' }}>
          Biến cũng có thể được sử dụng trong tham số Trường Văn Bản của bất kỳ nút nào. Ví dụ, trong Tin Nhắn Hệ Thống của Agent:
        </p>
        <CodeEditor
          disabled={true}
          value={`You are a {{$vars.personality}} AI assistant`}
          height={'50px'}
          theme={'dark'}
          lang={'js'}
          basicSetup={{ highlightActiveLine: false, highlightActiveLineGutter: false }}
        />
        <p style={{ marginBottom: '10px' }}>
          Nếu loại biến là Tĩnh, giá trị sẽ được lấy như hiện tại. Nếu loại biến là Thời Gian Chạy, giá trị sẽ được lấy từ tệp .env.
        </p>
        <p style={{ marginBottom: '10px' }}>
          Bạn cũng có thể ghi đè giá trị biến trong API overrideConfig bằng cách sử dụng <b>vars</b>:
        </p>
        <CodeEditor
          disabled={true}
          value={overrideConfig}
          height={'170px'}
          theme={'dark'}
          lang={'js'}
          basicSetup={{ highlightActiveLine: false, highlightActiveLineGutter: false }}
        />
        <p>
          Read more from{' '}
          <a target='_blank' rel='noreferrer' href='https://docs.flowiseai.com/using-flowise/variables'>
            docs
          </a>
        </p>
      </DialogContent>
    </Dialog>
  ) : null

  return createPortal(component, portalElement)
}

HowToUseVariablesDialog.propTypes = {
  show: PropTypes.bool,
  onCancel: PropTypes.func
}

export default HowToUseVariablesDialog
