export const blacklistCategoriesForAgentCanvas = ['Agents', 'Memory', 'Record Manager']

export const allowedAgentModel = {}

export const exceptions = {
  Memory: ['agentMemory']
}

export const tabOptions = [
  { label: 'Tất <PERSON>', icon: 'apps', size: 20 },
  { label: 'Tiện <PERSON>ch', icon: 'tool', size: 20 }
]

export function a11yProps(index) {
  return {
    id: `attachment-tab-${index}`,
    'aria-controls': `attachment-tabpanel-${index}`
  }
}
