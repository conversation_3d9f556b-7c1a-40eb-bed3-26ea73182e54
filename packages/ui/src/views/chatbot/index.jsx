import { FullPageChat } from 'cmcts-c-agent-embedding-react'
import { useEffect, useRef, useState } from 'react'
import { ClickAwayListener, createTheme, Skeleton, ThemeProvider, Typography } from '@mui/material'
import { styled } from '@mui/system'
import chatflowsApi from '@/api/chatflows'
import useApi from '@/hooks/useApi'
import { baseURL } from '@/store/constant'
import { useDispatch, useSelector } from 'react-redux'
import LoginVIB from '../Login/LoginVIB'
import { logoutAction } from '@/store/actions'
import { IconLogout, IconUserFilled } from '@tabler/icons-react'
import VIBLogo from '@/assets/images/VIB_WHITE.png'

window.VITE_DOCUMENT_STORE_BASE_URL = import.meta.env.VITE_DOCUMENT_STORE_BASE_URL || 'https://s3-explorer.cmcts1.studio.ai.vn'

// Custom theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2'
    },
    secondary: {
      main: '#dc004e'
    }
  },
  typography: {
    fontFamily: 'Roboto, sans-serif'
  }
})

// Custom styles
const Root = styled('div')(({ theme }) => ({}))

const ErrorMessage = styled('p')(({ theme }) => ({
  color: theme.palette.error.main,
  fontSize: '1.2rem',
  fontWeight: 'bold',
  animation: 'fadeIn 0.5s ease-in-out',
  '@keyframes fadeIn': {
    '0%': {
      opacity: 0
    },
    '100%': {
      opacity: 1
    }
  }
}))

const ChatbotFull = () => {
  const URLpath = document.location.pathname.toString().split('/')
  const chatflowId = URLpath[URLpath.length - 1] === 'chatbot' ? '' : URLpath[URLpath.length - 1]
  const dispatch = useDispatch()
  const logout = (...args) => dispatch(logoutAction(...args))
  const user = useSelector((state) => state.user)
  const [chatflow, setChatflow] = useState(null)
  const [chatbotTheme, setChatbotTheme] = useState({})
  const [chatbotOverrideConfig, setChatbotOverrideConfig] = useState({})
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const dropdownRef = useRef(null)

  const getSpecificChatflowFromPublicApi = useApi(chatflowsApi.getSpecificChatflowFromPublicEndpoint)

  const handleLogout = () => {
    localStorage.removeItem('dataLogin')
    logout({})
    window.clearChat()
    // window.location.reload()
  }

  const handleClickAway = () => {
    setDropdownOpen(false)
  }

  const error = getSpecificChatflowFromPublicApi?.error
  useEffect(() => {
    getSpecificChatflowFromPublicApi.request(chatflowId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (getSpecificChatflowFromPublicApi?.data) {
      const chatflowData = getSpecificChatflowFromPublicApi.data
      setChatflow(chatflowData)

      const chatflowType = chatflowData.type
      if (chatflowData.chatbotConfig) {
        let parsedConfig = {}
        if (chatflowType === 'MULTIAGENT') {
          parsedConfig.showAgentMessages = true
        }

        try {
          parsedConfig = { ...parsedConfig, ...JSON.parse(chatflowData.chatbotConfig) }
          setChatbotTheme(parsedConfig)
          if (parsedConfig.overrideConfig) {
            if (parsedConfig.overrideConfig.generateNewSession) {
              parsedConfig.overrideConfig.sessionId = Date.now().toString()
            }
            setChatbotOverrideConfig(parsedConfig.overrideConfig)
          }
        } catch (e) {
          console.error(e)
          setChatbotTheme(parsedConfig)
          setChatbotOverrideConfig({})
        }
      } else if (chatflowType === 'MULTIAGENT') {
        setChatbotTheme({ showAgentMessages: true })
      }
    }
  }, [getSpecificChatflowFromPublicApi.data])

  useEffect(() => {
    if (chatflow?.id) {
      const isUseFAQ = Boolean(chatflow?.flowData.includes('"label":"FAQs","name":"faqs","version":1,"type":"FAQs"'))
      const existingChatflowData = JSON.parse(localStorage?.getItem('isUseFAQs')) || {}
      const updatedChatflowData = { ...existingChatflowData, [chatflow.id]: isUseFAQ }
      localStorage.setItem('isUseFAQs', JSON.stringify(updatedChatflowData))
    }
  }, [chatflow?.id])

  if (chatflow?.groupname === 'VIB' && user?.groupname !== 'VIB' && user?.groupname !== 'Master_admin') {
    return <LoginVIB />
  }

  return (
    <ThemeProvider theme={theme}>
      <Root className='relative w-full h-full'>
        {chatflow?.groupname === 'VIB' && user?.username && (
          <>
            <div className='absolute top-[-15px] left-14 z-50'>
              <Typography component='h1' variant='h3' color='white' fontWeight='bold' mb={2}>
                <img src={VIBLogo} alt='VIB Logo' style={{ maxWidth: '80px', height: 'auto' }} />
              </Typography>
            </div>

            <div className='absolute top-[5px] right-14 z-50'>
              <ClickAwayListener onClickAway={handleClickAway}>
                <div className='relative'>
                  <div
                    onClick={() => setDropdownOpen(!dropdownOpen)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        setDropdownOpen(!dropdownOpen)
                      }
                    }}
                    role='button'
                    tabIndex={0}
                    className='flex items-center gap-4 py-2 px-1 rounded-lg border border-white/30 cursor-pointer hover:bg-white/10 transition-all duration-200'
                  >
                    <div className='text-sm text-white flex items-center gap-1'>
                      <IconUserFilled /> {user?.username}
                      <svg
                        xmlns='http://www.w3.org/2000/svg'
                        fill='none'
                        viewBox='0 0 24 24'
                        strokeWidth={2}
                        stroke='currentColor'
                        className={`w-3 h-3 ml-1 transition-transform duration-200 ${dropdownOpen ? 'rotate-180' : ''}`}
                      >
                        <path strokeLinecap='round' strokeLinejoin='round' d='M19.5 8.25l-7.5 7.5-7.5-7.5' />
                      </svg>
                    </div>
                  </div>

                  {dropdownOpen && (
                    <div
                      ref={dropdownRef}
                      className='absolute right-[-10px] mt-1 w-40 rounded-md shadow-lg z-50 animate-fadeIn border-none'
                      style={{ animation: 'fadeIn 0.2s ease-in-out' }}
                    >
                      <button
                        onClick={handleLogout}
                        className='flex items-center gap-2 w-full rounded-md px-4 py-1 text-sm hover:bg-white/10 transition-all border-none cursor-pointer'
                      >
                        <IconLogout />
                        Đăng xuất
                      </button>
                    </div>
                  )}
                </div>
              </ClickAwayListener>
            </div>
          </>
        )}
        {getSpecificChatflowFromPublicApi.loading ? (
          <div className='w-full flex justify-center'>
            <Skeleton className='w-full flex justify-center justify-items-center' variant='rectangular' width='100%' height={400} />
          </div>
        ) : (
          <>
            {!chatflow?.id && error ? (
              <div className='flex w-full justify-center'>
                <ErrorMessage>{error.response.data.message}</ErrorMessage>
              </div>
            ) : (
              chatflow?.id && (
                <FullPageChat
                  chatflowid={chatflow?.id}
                  apiHost={baseURL}
                  chatflowConfig={chatbotOverrideConfig}
                  theme={{ chatWindow: chatbotTheme }}
                  isUseFAQ={Boolean(chatflow?.isUseFAQ)}
                />
              )
            )}
          </>
        )}
      </Root>
    </ThemeProvider>
  )
}

export default ChatbotFull
