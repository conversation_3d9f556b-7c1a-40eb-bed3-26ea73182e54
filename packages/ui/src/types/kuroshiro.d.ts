declare module 'kuroshiro' {
  interface ConvertOptions {
    to?: 'hiragana' | 'katakana' | 'romaji'
    mode?: 'normal' | 'spaced' | 'okurigana' | 'furigana'
    romajiSystem?: 'nippon' | 'passport' | 'hepburn'
    delimiter_start?: string
    delimiter_end?: string
  }

  class <PERSON><PERSON><PERSON> {
    constructor()
    init(analyzer: any): Promise<void>
    convert(text: string, options?: ConvertOptions): Promise<string>
  }

  export default <PERSON><PERSON><PERSON>
}

declare module 'kuroshiro-analyzer-kuromoji' {
  interface KuromojiAnalyzerOptions {
    dictPath?: string
  }

  class KuromojiAnalyzer {
    constructor(options?: KuromojiAnalyzerOptions)
  }

  export default KuromojiAnalyzer
}
