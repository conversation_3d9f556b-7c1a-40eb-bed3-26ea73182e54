// assets
import {
  IconTrash,
  IconFileUpload,
  IconFileExport,
  IconCopy,
  IconMessage,
  IconDatabaseExport,
  IconAdjustmentsHorizontal,
  IconUsers,
  IconTemplate
} from '@tabler/icons-react'

// constant
const icons = {
  IconTrash,
  IconFileUpload,
  IconFileExport,
  IconCopy,
  IconMessage,
  IconDatabaseExport,
  IconAdjustmentsHorizontal,
  IconUsers,
  IconTemplate
}

// ==============================|| SETTINGS MENU ITEMS ||============================== //

const agent_settings = {
  id: 'settings',
  title: '',
  type: 'group',
  children: [
    {
      id: 'viewMessages',
      title: 'Xem Tin Nhắn',
      type: 'item',
      url: '',
      icon: icons.IconMessage
    },
    {
      id: 'viewLeads',
      title: '<PERSON><PERSON>ề<PERSON>ng',
      type: 'item',
      url: '',
      icon: icons.IconUsers
    },
    {
      id: 'chatflowConfiguration',
      title: 'Cấu <PERSON>',
      type: 'item',
      url: '',
      icon: icons.IconAdjustmentsHorizontal
    },
    {
      id: 'saveAsTemplate',
      title: 'Lưu Thành Mẫu',
      type: 'item',
      url: '',
      icon: icons.IconTemplate
    },
    {
      id: 'duplicateChatflow',
      title: 'Nhân Bản Agent',
      type: 'item',
      url: '',
      icon: icons.IconCopy
    },
    {
      id: 'loadChatflow',
      title: 'Tải Agent',
      type: 'item',
      url: '',
      icon: icons.IconFileUpload
    },
    {
      id: 'exportChatflow',
      title: 'Xuất Agent',
      type: 'item',
      url: '',
      icon: icons.IconFileExport
    },
    {
      id: 'deleteChatflow',
      title: 'Xóa Agent',
      type: 'item',
      url: '',
      icon: icons.IconTrash
    }
  ]
}

export default agent_settings
